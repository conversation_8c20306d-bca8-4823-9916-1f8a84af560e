#!/usr/bin/env python3
"""
网络代理配置修复脚本

解决LLM连接中的代理问题，提供多种解决方案。
"""

import os
import sys
import asyncio
import httpx
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def check_proxy_environment():
    """检查代理环境变量"""
    print("🔍 检查代理环境变量")
    print("-" * 50)
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    found_proxies = {}
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            found_proxies[var] = value
            print(f"📋 {var}: {value}")
    
    if found_proxies:
        print(f"\n⚠️  发现 {len(found_proxies)} 个代理配置")
        return found_proxies
    else:
        print("✅ 未发现代理环境变量")
        return {}


def clear_proxy_environment():
    """清除代理环境变量"""
    print("\n🧹 清除代理环境变量")
    print("-" * 50)
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    cleared = []
    
    for var in proxy_vars:
        if var in os.environ:
            del os.environ[var]
            cleared.append(var)
            print(f"🗑️  已清除: {var}")
    
    if cleared:
        print(f"✅ 已清除 {len(cleared)} 个代理变量")
    else:
        print("ℹ️  没有需要清除的代理变量")
    
    return cleared


async def test_direct_connection():
    """测试直接连接（无代理）"""
    print("\n🌐 测试直接连接")
    print("-" * 50)
    
    test_urls = [
        "https://api.openai.com/v1/models",
        "https://api.siliconflow.cn/v1/models", 
        "https://open.bigmodel.cn/api/paas/v4/models"
    ]
    
    results = {}
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        for url in test_urls:
            try:
                print(f"🔗 测试连接: {url}")
                response = await client.get(url)
                results[url] = {
                    "status": "success",
                    "status_code": response.status_code,
                    "accessible": True
                }
                print(f"✅ 连接成功 (状态码: {response.status_code})")
            except Exception as e:
                results[url] = {
                    "status": "failed", 
                    "error": str(e),
                    "accessible": False
                }
                print(f"❌ 连接失败: {str(e)[:50]}...")
    
    return results


def create_no_proxy_env_file():
    """创建无代理环境配置文件"""
    print("\n📝 创建无代理环境配置")
    print("-" * 50)
    
    env_file = Path(".env.no_proxy")
    
    content = """# 无代理环境配置
# 清除所有代理设置
HTTP_PROXY=
HTTPS_PROXY=
http_proxy=
https_proxy=
ALL_PROXY=
all_proxy=
NO_PROXY=localhost,127.0.0.1,::1

# 强制禁用代理
HTTPX_DISABLE_PROXY=1
REQUESTS_CA_BUNDLE=
CURL_CA_BUNDLE=
"""
    
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 已创建: {env_file}")
        print("📋 使用方法: 在启动应用前执行 'source .env.no_proxy' (Linux/Mac) 或手动设置环境变量")
        return True
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False


def create_httpx_config():
    """创建HTTPX客户端配置修复"""
    print("\n⚙️ 创建HTTPX配置修复")
    print("-" * 50)
    
    config_file = Path("src/utils/http_client.py")
    config_file.parent.mkdir(parents=True, exist_ok=True)
    
    content = '''"""
HTTP客户端配置 - 解决代理问题

提供无代理的HTTP客户端配置，用于LLM API调用。
"""

import httpx
import os
from typing import Optional, Dict, Any


def create_no_proxy_client(timeout: float = 30.0) -> httpx.AsyncClient:
    """
    创建无代理的异步HTTP客户端
    
    Args:
        timeout: 超时时间（秒）
        
    Returns:
        httpx.AsyncClient: 配置好的HTTP客户端
    """
    # 强制禁用代理
    return httpx.AsyncClient(
        timeout=timeout,
        proxies={},  # 空代理字典，强制禁用代理
        verify=True,  # 启用SSL验证
        follow_redirects=True
    )


def patch_openai_client():
    """
    修补OpenAI客户端以禁用代理
    
    这个函数会修改OpenAI客户端的默认HTTP客户端配置
    """
    try:
        import openai
        from openai._client import OpenAI, AsyncOpenAI
        
        # 保存原始的初始化方法
        original_openai_init = OpenAI.__init__
        original_async_openai_init = AsyncOpenAI.__init__
        
        def patched_openai_init(self, *args, **kwargs):
            # 强制设置无代理的HTTP客户端
            if 'http_client' not in kwargs:
                kwargs['http_client'] = httpx.Client(
                    proxies={},
                    timeout=30.0,
                    verify=True
                )
            return original_openai_init(self, *args, **kwargs)
        
        def patched_async_openai_init(self, *args, **kwargs):
            # 强制设置无代理的异步HTTP客户端
            if 'http_client' not in kwargs:
                kwargs['http_client'] = httpx.AsyncClient(
                    proxies={},
                    timeout=30.0,
                    verify=True
                )
            return original_async_openai_init(self, *args, **kwargs)
        
        # 应用补丁
        OpenAI.__init__ = patched_openai_init
        AsyncOpenAI.__init__ = patched_async_openai_init
        
        print("✅ OpenAI客户端代理补丁已应用")
        return True
        
    except ImportError:
        print("⚠️  OpenAI库未安装，跳过补丁")
        return False
    except Exception as e:
        print(f"❌ 应用OpenAI补丁失败: {e}")
        return False


def apply_global_no_proxy():
    """
    应用全局无代理设置
    
    清除所有代理环境变量并设置无代理标志
    """
    # 清除代理环境变量
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    for var in proxy_vars:
        if var in os.environ:
            del os.environ[var]
    
    # 设置无代理标志
    os.environ['NO_PROXY'] = 'localhost,127.0.0.1,::1'
    os.environ['HTTPX_DISABLE_PROXY'] = '1'
    
    print("✅ 全局无代理设置已应用")


# 自动应用修复（当模块被导入时）
if __name__ != "__main__":
    apply_global_no_proxy()
    patch_openai_client()
'''
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 已创建: {config_file}")
        return True
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False


def update_agents_utils():
    """更新agents utils以使用无代理配置"""
    print("\n🔧 更新智能体工具配置")
    print("-" * 50)
    
    utils_file = Path("src/agents/utils.py")
    
    if not utils_file.exists():
        print(f"❌ 文件不存在: {utils_file}")
        return False
    
    try:
        # 读取现有内容
        with open(utils_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 在文件开头添加代理修复导入
        if "from src.utils.http_client import apply_global_no_proxy" not in content:
            import_line = "from src.utils.http_client import apply_global_no_proxy, patch_openai_client\n"
            
            # 找到第一个import语句的位置
            lines = content.split('\n')
            insert_pos = 0
            for i, line in enumerate(lines):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    insert_pos = i
                    break
            
            # 插入导入语句
            lines.insert(insert_pos, import_line)
            lines.insert(insert_pos + 1, "")
            
            # 在load_chat_model函数开头添加代理修复调用
            for i, line in enumerate(lines):
                if "def load_chat_model(" in line:
                    # 找到函数体开始
                    for j in range(i + 1, len(lines)):
                        if lines[j].strip() and not lines[j].startswith(' ') * 4:
                            continue
                        if lines[j].strip().startswith('"""') or lines[j].strip().startswith("'''"):
                            # 跳过文档字符串
                            continue
                        if lines[j].strip() and lines[j].startswith(' '):
                            # 在第一行实际代码前插入
                            lines.insert(j, "    # 应用无代理配置")
                            lines.insert(j + 1, "    apply_global_no_proxy()")
                            lines.insert(j + 2, "    patch_openai_client()")
                            lines.insert(j + 3, "")
                            break
                    break
            
            # 写回文件
            with open(utils_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            
            print("✅ 已更新 agents/utils.py")
            return True
        else:
            print("ℹ️  代理修复已存在于 agents/utils.py")
            return True
            
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False


async def main():
    """主函数"""
    print("🚀 网络代理配置修复工具")
    print("=" * 60)
    
    # 1. 检查当前代理配置
    proxy_config = check_proxy_environment()
    
    # 2. 如果发现代理配置，提供解决方案
    if proxy_config:
        print("\n🔧 检测到代理配置，提供以下解决方案:")
        print("1. 临时清除代理环境变量")
        print("2. 创建无代理配置文件")
        print("3. 修补应用代码以禁用代理")
        
        # 清除代理环境变量
        cleared = clear_proxy_environment()
        
        # 测试直接连接
        if cleared:
            print("\n⏳ 测试清除代理后的连接...")
            connection_results = await test_direct_connection()
            
            success_count = sum(1 for result in connection_results.values() if result.get('accessible', False))
            total_count = len(connection_results)
            
            if success_count > 0:
                print(f"✅ 清除代理后，{success_count}/{total_count} 个连接成功")
            else:
                print("❌ 清除代理后仍无法连接，可能是网络问题")
    
    # 3. 创建配置文件和代码修复
    print("\n🛠️ 创建持久化修复方案...")
    
    # 创建无代理环境文件
    create_no_proxy_env_file()
    
    # 创建HTTP客户端配置
    create_httpx_config()
    
    # 更新智能体工具
    update_agents_utils()
    
    # 4. 提供使用建议
    print("\n" + "=" * 60)
    print("🎯 修复完成！使用建议:")
    print()
    print("1. 🔄 重启应用以应用修复")
    print("2. 🧪 运行 'python test_llm_connection.py' 验证连接")
    print("3. 📝 如果仍有问题，手动设置环境变量:")
    print("   export HTTP_PROXY=")
    print("   export HTTPS_PROXY=")
    print("   export http_proxy=")
    print("   export https_proxy=")
    print()
    print("4. 🔧 或者在启动前加载无代理配置:")
    print("   source .env.no_proxy  # Linux/Mac")
    print("   # 或在Windows中手动设置环境变量")


if __name__ == "__main__":
    asyncio.run(main())
