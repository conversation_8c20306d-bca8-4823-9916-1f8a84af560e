#!/usr/bin/env python3
"""
LLM连接诊断测试脚本

用于诊断和测试LLM连接问题，验证API密钥、网络连接和模型配置。
"""

import os
import sys
import asyncio
import traceback
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logging_config import logger
from src.agents.utils import load_chat_model
from src.config import Config
from langchain_core.messages import HumanMessage, AIMessage


async def test_model_connection(model_name: str):
    """测试特定模型的连接"""
    print(f"\n🔍 测试模型: {model_name}")
    print("-" * 50)
    
    try:
        # 1. 加载模型
        print("1. 加载模型...")
        model = load_chat_model(model_name)
        print(f"✅ 模型加载成功: {type(model).__name__}")
        
        # 2. 测试简单调用
        print("2. 测试模型调用...")
        test_message = [HumanMessage(content="你好，请回复'测试成功'")]
        
        response = await model.ainvoke(test_message)
        print(f"✅ 模型调用成功")
        print(f"📝 响应类型: {type(response).__name__}")
        print(f"📝 响应内容: {response.content[:100]}...")
        
        # 3. 测试JSON序列化
        print("3. 测试JSON序列化...")
        try:
            if hasattr(response, 'model_dump'):
                serialized = response.model_dump()
                json_str = json.dumps(serialized, ensure_ascii=False)
                print("✅ JSON序列化成功")
            else:
                print("⚠️  响应对象没有model_dump方法")
        except Exception as e:
            print(f"❌ JSON序列化失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        print(f"📋 错误详情: {traceback.format_exc()}")
        return False


async def test_environment_variables():
    """测试环境变量配置"""
    print("\n🔧 检查环境变量配置")
    print("-" * 50)
    
    # 从配置文件加载模型信息
    config = Config()
    
    print("📋 已配置的模型提供商:")
    for provider, status in config.model_provider_status.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {provider}: {status}")
        
        if not status:
            # 显示缺失的环境变量
            env_vars = config.model_names.get(provider, {}).get("env", [])
            print(f"    缺失环境变量: {env_vars}")
    
    print(f"\n🎯 可用的模型提供商: {config.valuable_model_provider}")
    return len(config.valuable_model_provider) > 0


async def test_chatbot_agent():
    """测试聊天机器人智能体"""
    print("\n🤖 测试聊天机器人智能体")
    print("-" * 50)
    
    try:
        # 导入智能体
        from src.agents.chatbot.graph import ChatbotAgent
        from src.agents.chatbot.configuration import ChatbotConfiguration
        from langchain_core.runnables import RunnableConfig
        
        # 创建智能体实例
        print("1. 创建智能体实例...")
        agent = ChatbotAgent()
        print("✅ 智能体创建成功")
        
        # 创建测试配置
        print("2. 创建测试配置...")
        config = RunnableConfig(configurable={
            "thread_id": "test_thread",
            "user_id": "test_user"
        })
        
        # 测试LLM调用
        print("3. 测试LLM调用...")
        test_state = {
            "messages": [HumanMessage(content="你好，请简单回复")]
        }
        
        result = await agent.llm_call(test_state, config)
        print("✅ LLM调用成功")
        print(f"📝 响应消息数量: {len(result.get('messages', []))}")
        
        if result.get('messages'):
            msg = result['messages'][0]
            print(f"📝 响应类型: {type(msg).__name__}")
            print(f"📝 响应内容: {msg.content[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能体测试失败: {e}")
        print(f"📋 错误详情: {traceback.format_exc()}")
        return False


async def test_streaming_serialization():
    """测试流式响应的JSON序列化"""
    print("\n📡 测试流式响应序列化")
    print("-" * 50)
    
    try:
        from langchain_core.messages import AIMessage, AIMessageChunk
        
        # 测试AIMessage序列化
        print("1. 测试AIMessage序列化...")
        ai_msg = AIMessage(content="测试消息")
        
        try:
            serialized = ai_msg.model_dump()
            json_str = json.dumps(serialized, ensure_ascii=False)
            print("✅ AIMessage序列化成功")
        except Exception as e:
            print(f"❌ AIMessage序列化失败: {e}")
        
        # 测试AIMessageChunk序列化
        print("2. 测试AIMessageChunk序列化...")
        ai_chunk = AIMessageChunk(content="测试块")
        
        try:
            serialized = ai_chunk.model_dump()
            json_str = json.dumps(serialized, ensure_ascii=False)
            print("✅ AIMessageChunk序列化成功")
        except Exception as e:
            print(f"❌ AIMessageChunk序列化失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 序列化测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 LLM连接诊断测试开始")
    print("=" * 60)
    
    # 1. 检查环境变量
    env_ok = await test_environment_variables()
    
    if not env_ok:
        print("\n❌ 环境变量配置不完整，请检查.env文件")
        return
    
    # 2. 测试流式序列化
    await test_streaming_serialization()
    
    # 3. 测试模型连接
    config = Config()
    test_models = []
    
    # 选择要测试的模型
    if "zhipu" in config.valuable_model_provider:
        test_models.append("zhipu/glm-4-plus")
    if "siliconflow" in config.valuable_model_provider:
        test_models.append("siliconflow/Qwen/Qwen3-8B")
    if "openai" in config.valuable_model_provider:
        test_models.append("openai/gpt-3.5-turbo")
    
    if not test_models:
        print("\n❌ 没有可用的模型进行测试")
        return
    
    # 测试每个模型
    success_count = 0
    for model_name in test_models:
        success = await test_model_connection(model_name)
        if success:
            success_count += 1
    
    # 4. 测试智能体
    if success_count > 0:
        await test_chatbot_agent()
    
    # 总结
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    print(f"📊 模型测试成功率: {success_count}/{len(test_models)}")
    
    if success_count == 0:
        print("❌ 所有模型测试失败，请检查网络连接和API密钥")
    elif success_count < len(test_models):
        print("⚠️  部分模型测试失败，建议检查相关配置")
    else:
        print("✅ 所有测试通过，LLM连接正常")


if __name__ == "__main__":
    asyncio.run(main())
