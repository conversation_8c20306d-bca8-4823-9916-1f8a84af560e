import os
import uuid
from typing import Any, TYPE_CHECKING, Optional
from pathlib import Path
from datetime import datetime, timezone

import sqlite3
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph, START, END
from langgraph.prebuilt import ToolNode, tools_condition
from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver, aiosqlite

from src import config as sys_config
from src.utils import logger
from src.agents.registry import State, BaseAgent
from src.agents.utils import load_chat_model, get_cur_time_with_utc
from src.agents.chatbot.configuration import ChatbotConfiguration
from src.agents.tools_factory import get_all_tools

if TYPE_CHECKING:
    from src.agents.context import UserContext

class ChatbotAgent(BaseAgent):
    """
    重构后的聊天机器人智能体
    
    集成权限控制、用户上下文和统一数据库管理的企业级聊天机器人。
    支持动态工具加载、权限过滤和个性化配置。
    """
    
    name = "chatbot"
    description = "权限感知的聊天机器人智能体，支持多轮对话、工具调用和个性化配置"
    requirements = ["TAVILY_API_KEY", "ZHIPUAI_API_KEY"]
    config_schema = ChatbotConfiguration

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 图实例缓存在父类中管理
        self.workdir = Path(sys_config.save_dir) / "agents" / self.name
        self.workdir.mkdir(parents=True, exist_ok=True)
        
        logger.debug(f"ChatbotAgent 初始化完成，工作目录: {self.workdir}")

    async def _get_user_tools(self, user_context: Optional['UserContext'] = None):
        """
        获取用户可用工具 - 权限感知
        
        Args:
            user_context: 用户上下文，用于权限过滤
            
        Returns:
            list: 用户可用工具列表
        """
        if user_context:
            # 使用权限感知的工具获取
            try:
                user_tools = await self.get_user_tools(user_context)
                tool_list = list(user_tools.values())
                logger.info(f"用户 {user_context.user_id} 可用工具: {list(user_tools.keys())}")
                return tool_list
            except Exception as e:
                logger.warning(f"获取用户工具失败，使用降级方案: {e}")
                return self._get_tools_legacy([])
        else:
            # 降级到旧版本工具获取（向后兼容）
            return self._get_tools_legacy([])
    
    def _get_tools_legacy(self, tools: list[str]):
        """
        旧版本工具获取 - 向后兼容
        
        Args:
            tools: 工具名称列表
            
        Returns:
            list: 工具实例列表
        """
        platform_tools = get_all_tools()
        if tools is None or not isinstance(tools, list) or len(tools) == 0:
            # 默认不使用任何工具
            logger.info("未配置工具或配置为空，不使用任何工具")
            return []
        else:
            # 使用配置中指定的工具
            tool_names = [tool for tool in platform_tools.keys() if tool in tools]
            logger.info(f"使用工具: {tool_names}")
            return [platform_tools[tool] for tool in tool_names]

    async def llm_call(self, state: State, config: RunnableConfig = None) -> dict[str, Any]:
        """
        调用 LLM 模型 - 权限感知版本
        
        Args:
            state: 对话状态
            config: 运行时配置
            
        Returns:
            dict: 包含响应消息的状态更新
        """
        # 获取用户上下文
        user_context = config.get("configurable", {}).get("user_context") if config else None
        
        # 权限检查
        if user_context and not await self.check_user_permission(user_context, "execute"):
            raise PermissionError(f"用户无权执行智能体: {self.name}")
        
        # 获取配置（支持用户上下文）
        conf = await self.config_schema.from_runnable_config(
            config, agent_name=self.name, user_context=user_context
        )

        # 构建系统提示
        system_prompt = f"{conf.system_prompt} 当前时间: {get_cur_time_with_utc()}"
        
        # 加载模型
        model = load_chat_model(conf.model)

        # 获取用户可用工具
        user_tools = await self._get_user_tools(user_context)
        if user_tools:
            model = model.bind_tools(user_tools)
            logger.debug(f"为用户 {user_context.user_id if user_context else 'anonymous'} 绑定了 {len(user_tools)} 个工具")

        # 调用模型 - 带重试和降级策略
        return await self._call_llm_with_retry(
            model, system_prompt, state["messages"], user_context
        )

    async def _call_llm_with_retry(self, model, system_prompt: str, messages: list, user_context=None, max_retries: int = 3):
        """
        带重试机制的LLM调用

        Args:
            model: LLM模型实例
            system_prompt: 系统提示词
            messages: 消息列表
            user_context: 用户上下文
            max_retries: 最大重试次数

        Returns:
            dict: 包含响应消息的状态更新
        """
        import asyncio
        from langchain_core.messages import AIMessage

        last_error = None

        for attempt in range(max_retries + 1):
            try:
                # 构建完整的消息列表
                full_messages = [{"role": "system", "content": system_prompt}, *messages]

                # 调用模型
                res = await model.ainvoke(full_messages)

                # 成功返回
                logger.debug(f"LLM调用成功，尝试次数: {attempt + 1}")
                return {"messages": [res]}

            except Exception as e:
                last_error = e
                error_msg = str(e)

                # 记录错误
                logger.warning(f"LLM调用失败 (尝试 {attempt + 1}/{max_retries + 1}): {error_msg}")

                # 如果不是最后一次尝试，等待后重试
                if attempt < max_retries:
                    # 根据错误类型决定是否重试
                    if self._should_retry(e):
                        wait_time = min(2 ** attempt, 10)  # 指数退避，最大10秒
                        logger.info(f"等待 {wait_time} 秒后重试...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        # 不可重试的错误，直接跳出
                        logger.error(f"遇到不可重试的错误: {error_msg}")
                        break

        # 所有重试都失败，返回降级响应
        return await self._get_fallback_response(last_error, user_context)

    def _should_retry(self, error: Exception) -> bool:
        """
        判断错误是否应该重试

        Args:
            error: 异常对象

        Returns:
            bool: 是否应该重试
        """
        error_msg = str(error).lower()

        # 网络相关错误可以重试
        if any(keyword in error_msg for keyword in [
            "connection error", "timeout", "network", "proxy error",
            "connection refused", "connection reset", "read timeout"
        ]):
            return True

        # API限流错误可以重试
        if any(keyword in error_msg for keyword in [
            "rate limit", "too many requests", "quota exceeded"
        ]):
            return True

        # 服务器错误可以重试
        if any(keyword in error_msg for keyword in [
            "internal server error", "service unavailable", "bad gateway"
        ]):
            return True

        # 其他错误不重试（如认证错误、权限错误等）
        return False

    async def _get_fallback_response(self, error: Exception, user_context=None):
        """
        获取降级响应

        Args:
            error: 最后一次的错误
            user_context: 用户上下文

        Returns:
            dict: 降级响应
        """
        from langchain_core.messages import AIMessage

        error_msg = str(error)

        # 根据错误类型提供不同的降级消息
        if "connection error" in error_msg.lower() or "proxy error" in error_msg.lower():
            fallback_content = (
                "抱歉，当前网络连接不稳定，无法正常处理您的请求。\n"
                "可能的解决方案：\n"
                "1. 检查网络连接\n"
                "2. 检查代理设置\n"
                "3. 稍后再试\n"
                "如果问题持续存在，请联系系统管理员。"
            )
        elif "api" in error_msg.lower():
            fallback_content = (
                "抱歉，AI服务暂时不可用。\n"
                "这可能是由于：\n"
                "1. API密钥配置问题\n"
                "2. 服务提供商维护\n"
                "3. 请求配额已用完\n"
                "请稍后再试或联系管理员。"
            )
        elif "permission" in error_msg.lower():
            fallback_content = (
                "抱歉，您没有权限使用此功能。\n"
                "请联系管理员获取相应权限。"
            )
        else:
            fallback_content = (
                f"抱歉，处理您的请求时出现了技术问题。\n"
                f"错误信息：{error_msg}\n"
                f"请稍后再试，如果问题持续存在，请联系技术支持。"
            )

        # 记录详细错误信息
        logger.error(f"LLM调用最终失败，返回降级响应: {error}")

        error_response = AIMessage(content=fallback_content)
        return {"messages": [error_response]}

    async def get_graph(self,
                       config: RunnableConfig = None, 
                       user_context: Optional['UserContext'] = None, 
                       **kwargs):
        """
        获取聊天机器人图 - 权限上下文感知
        
        Args:
            config: 运行时配置
            user_context: 用户上下文
            **kwargs: 其他参数
            
        Returns:
            CompiledStateGraph: 编译后的状态图
        """
        # 权限检查
        if user_context and not await self.check_user_permission(user_context, "access"):
            raise PermissionError(f"用户无权访问智能体: {self.name}")
        
        # 检查是否需要重新构建图（基于用户上下文）
        cache_key = f"graph_{user_context.user_id if user_context else 'default'}"
        if hasattr(self, '_graph_cache') and cache_key in self._graph_cache:
            return self._graph_cache[cache_key]
        
        # 初始化图缓存
        if not hasattr(self, '_graph_cache'):
            self._graph_cache = {}

        # 创建状态图
        workflow = StateGraph(State, config_schema=self.config_schema)
        workflow.add_node("chatbot", self.llm_call)
        
        # 获取用户可用工具
        user_tools = await self._get_user_tools(user_context)
        
        if user_tools:
            # 只有当用户有可用工具时才添加工具节点
            workflow.add_node("tools", ToolNode(tools=user_tools))
            
            # 设置条件边
            workflow.add_edge(START, "chatbot")
            workflow.add_conditional_edges("chatbot", tools_condition)
            workflow.add_edge("tools", "chatbot")
        else:
            # 无工具的简单流程
            workflow.add_edge(START, "chatbot")
        
        workflow.add_edge("chatbot", END)

        # 创建检查点存储器
        try:
            # 使用统一数据库管理器获取检查点
            checkpointer = await self._get_checkpointer()
            if checkpointer:
                graph = workflow.compile(checkpointer=checkpointer)
            else:
                # 降级处理：使用本地SQLite
                sqlite_checkpointer = AsyncSqliteSaver(await self.get_async_conn())
                graph = workflow.compile(checkpointer=sqlite_checkpointer)
            
            # 缓存图实例
            self._graph_cache[cache_key] = graph
            
            logger.debug(f"成功构建聊天机器人图，用户: {user_context.user_id if user_context else 'anonymous'}，工具数量: {len(user_tools)}")
            
            return graph
            
        except Exception as e:
            logger.error(f"构建图时出错: {e}")
            # 降级处理：返回无检查点的图
            graph = workflow.compile()
            self._graph_cache[cache_key] = graph
            
            logger.warning(f"使用降级模式构建图（无历史记录功能）")
            return graph

    async def get_async_conn(self) -> aiosqlite.Connection:
        """获取异步数据库连接"""
        return await aiosqlite.connect(os.path.join(self.workdir, "aio_history.db"))

    async def get_aio_memory(self) -> AsyncSqliteSaver:
        """获取异步存储实例"""
        return AsyncSqliteSaver(await self.get_async_conn())

def main():
    agent = ChatbotAgent(ChatbotConfiguration())

    thread_id = str(uuid.uuid4())
    config = {"configurable": {"thread_id": thread_id}}

    from src.agents.utils import agent_cli
    agent_cli(agent, config)


if __name__ == "__main__":
    main()
    # asyncio.run(main())
